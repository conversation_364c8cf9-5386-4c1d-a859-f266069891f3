<template>
  <section class="data-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">数据说话，实力见证</h2>
        <p class="section-subtitle">
          已为数千家中小电商企业提供专业的分账系统服务，累计处理资金超25亿元
        </p>
      </div>
      
      <div class="data-grid">
        <div class="data-card" v-for="(item, index) in dataItems" :key="index">
          <div class="card-icon">
            <el-icon :size="40">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-number" ref="numberRefs">{{ item.displayValue }}</div>
            <div class="card-label">{{ item.label }}</div>
            <div class="card-desc">{{ item.description }}</div>
          </div>
          <div class="card-trend" v-if="item.trend">
            <el-icon class="trend-icon" :class="item.trend.type">
              <component :is="item.trend.icon" />
            </el-icon>
            <span class="trend-text">{{ item.trend.text }}</span>
          </div>
        </div>
      </div>

      <!-- 合作银行展示 -->
      <div class="partners-section">
        <h3 class="partners-title">合作银行</h3>
        <div class="partners-grid">
          <div class="partner-item" v-for="bank in partnerBanks" :key="bank.name">
            <div class="partner-logo">
              <img :src="bank.logo" :alt="bank.name" />
            </div>
            <div class="partner-name">{{ bank.name }}</div>
          </div>
        </div>
      </div>

    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  TrendCharts, 
  Money, 
  Timer, 
  UserFilled,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

const numberRefs = ref([])

const dataItems = ref([
  {
    icon: UserFilled,
    value: 3200,
    displayValue: '3,200+',
    label: '服务企业',
    description: '覆盖电商、跨境、新零售等行业',
    trend: {
      type: 'up',
      icon: ArrowUp,
      text: '月增长 12%'
    }
  },
  {
    icon: Money,
    value: 25,
    displayValue: '25亿',
    label: '累计处理资金',
    description: '安全稳定的资金处理能力',
    trend: {
      type: 'up',
      icon: ArrowUp,
      text: '年增长 85%'
    }
  },
  {
    icon: Timer,
    value: 0,
    displayValue: 'T+0',
    label: '资金到账时效',
    description: '7×24小时实时到账服务',
    trend: null
  },
  {
    icon: TrendCharts,
    value: 99.9,
    displayValue: '99.9%',
    label: '系统稳定性',
    description: '银行级系统架构保障',
    trend: {
      type: 'stable',
      icon: ArrowUp,
      text: '持续稳定'
    }
  }
])

const partnerBanks = ref([
  {
    name: '百信银行',
    logo: '/banks/baixin.png'
  },
  {
    name: '苏商银行',
    logo: '/banks/sushang.png'
  },
  {
    name: '蓝海银行',
    logo: '/banks/lanhai.png'
  },
  {
    name: '新网银行',
    logo: '/banks/xinwang.png'
  },
  {
    name: '广州农商银行',
    logo: '/banks/nongshang.png'
  },
  {
    name: '平安银行',
    logo: '/banks/pingan.png'
  },
  {
    name: '兴业银行',
    logo: '/banks/xingye.png'
  },
  {
    name: '中国建设银行',
    logo: '/banks/jianshe.png'
  },
  {
    name: '招商银行',
    logo: '/banks/zhaoshang.png'
  },
  {
    name: '中国邮政储蓄银行',
    logo: '/banks/youchu.png'
  },
  {
    name: '中国工商银行',
    logo: '/banks/gongshang.png'
  }
])

// 数字动画效果
const animateNumbers = () => {
  dataItems.value.forEach((item, index) => {
    if (typeof item.value === 'number' && item.value > 0) {
      let current = 0
      const target = item.value
      const increment = target / 100
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          current = target
          clearInterval(timer)
        }
        
        if (item.label.includes('资金')) {
          item.displayValue = `${current.toFixed(0)}亿`
        } else if (item.label.includes('企业')) {
          item.displayValue = `${Math.floor(current).toLocaleString()}+`
        } else if (item.label.includes('稳定性')) {
          item.displayValue = `${current.toFixed(1)}%`
        }
      }, 20)
    }
  })
}

onMounted(() => {
  // 使用 Intersection Observer 来触发动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        animateNumbers()
        observer.disconnect()
      }
    })
  })
  
  const section = document.querySelector('.data-section')
  if (section) {
    observer.observe(section)
  }
})
</script>

<style scoped>
.data-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.data-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2.5rem;
  margin-bottom: 5rem;
}

@media (max-width: 1200px) {
  .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.data-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  margin-bottom: 1.5rem;
}

.card-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.card-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.card-desc {
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 20px;
  margin-top: 1rem;
}

.trend-icon {
  font-size: 1rem;
}

.trend-icon.up {
  color: #10b981;
}

.trend-icon.down {
  color: #ef4444;
}

.trend-icon.stable {
  color: #6b7280;
}

.trend-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #10b981;
}

/* 合作银行样式 */
.partners-section {
  text-align: center;
  padding-top: 3rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.partners-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2rem;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.partner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.partner-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.partner-logo {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  border: 2px solid #f1f5f9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.partner-logo:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: #e2e8f0;
}

.partner-logo img {
  width: 90%;
  height: 90%;
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.partner-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-section {
    padding: 80px 0;
  }

  .data-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .data-card {
    padding: 2rem;
  }

  .card-number {
    font-size: 2.5rem;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .partner-logo {
    width: 70px;
    height: 70px;
  }

  .partner-item {
    padding: 1rem;
  }
}
</style>
