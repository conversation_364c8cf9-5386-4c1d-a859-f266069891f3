<template>
  <section class="partner-banks-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">银行合作伙伴</h2>
        <p class="section-subtitle">
          携手知名银行机构，共建安全可信的金融服务生态
        </p>
      </div>
      
      <div class="carousel-container">
        <div class="carousel-wrapper">
          <div class="carousel-track" ref="carouselTrack" :style="{ transform: `translateX(-${currentIndex * slideWidth}px)` }">
            <div
              class="bank-slide"
              v-for="bank in banks"
              :key="bank.name"
              @click="handleBankClick(bank)"
            >
              <div class="bank-logo">
                <img :src="bank.logo" :alt="bank.name" />
              </div>
              <div class="bank-info">
                <h3 class="bank-name">{{ bank.name }}</h3>
                <p class="bank-desc">{{ bank.description }}</p>
              </div>
            </div>
          </div>

          <!-- 导航按钮 -->
          <button class="carousel-btn prev" @click="prevSlide" :disabled="currentIndex === 0">
            <el-icon><ArrowLeft /></el-icon>
          </button>
          <button class="carousel-btn next" @click="nextSlide" :disabled="currentIndex >= maxIndex">
            <el-icon><ArrowRight /></el-icon>
          </button>
        </div>

        <!-- 指示器 -->
        <div class="carousel-indicators">
          <button
            v-for="(dot, index) in indicatorCount"
            :key="index"
            class="indicator-dot"
            :class="{ active: Math.floor(currentIndex / slidesPerView) === index }"
            @click="goToSlide(index * slidesPerView)"
          ></button>
        </div>
      </div>
      
      <!-- 银行数量统计 -->
      <div class="bank-stats">
        <div class="stat-item">
          <div class="stat-number">{{ totalBanks }}+</div>
          <div class="stat-label">合作银行</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">100%</div>
          <div class="stat-label">资金安全保障</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">7×24</div>
          <div class="stat-label">服务时间</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { getPartnerBanks, getBankCount } from '../config/banks.js'

// 银行数据
const banks = ref(getPartnerBanks())

// 轮播相关状态
const carouselTrack = ref(null)
const currentIndex = ref(0)
const slidesPerView = ref(4) // 每次显示的银行数量
const slideWidth = ref(300) // 每个银行卡片的宽度
const autoplayInterval = ref(null)

// 计算属性
const maxIndex = computed(() => Math.max(0, banks.value.length - slidesPerView.value))
const indicatorCount = computed(() => Math.ceil(banks.value.length / slidesPerView.value))

// 轮播控制方法
const nextSlide = () => {
  if (currentIndex.value < maxIndex.value) {
    currentIndex.value++
  } else {
    currentIndex.value = 0 // 循环到开始
  }
}

const prevSlide = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else {
    currentIndex.value = maxIndex.value // 循环到结束
  }
}

const goToSlide = (index) => {
  currentIndex.value = Math.min(index, maxIndex.value)
}

// 自动播放
const startAutoplay = () => {
  autoplayInterval.value = setInterval(() => {
    nextSlide()
  }, 4000)
}

const stopAutoplay = () => {
  if (autoplayInterval.value) {
    clearInterval(autoplayInterval.value)
    autoplayInterval.value = null
  }
}

// 响应式处理
const updateSlidesPerView = () => {
  const width = window.innerWidth
  if (width < 768) {
    slidesPerView.value = 2
    slideWidth.value = 200
  } else if (width < 1024) {
    slidesPerView.value = 3
    slideWidth.value = 250
  } else {
    slidesPerView.value = 4
    slideWidth.value = 300
  }
}

// 银行总数
const totalBanks = computed(() => getBankCount())

// 点击银行处理
const handleBankClick = (bank) => {
  console.log('点击银行:', bank.name)
  ElMessage({
    message: `了解更多关于${bank.name}的合作详情`,
    type: 'info',
    duration: 2000
  })
}

// 生命周期
onMounted(() => {
  updateSlidesPerView()
  startAutoplay()
  window.addEventListener('resize', updateSlidesPerView)
})

onUnmounted(() => {
  stopAutoplay()
  window.removeEventListener('resize', updateSlidesPerView)
})
</script>

<style scoped>
.partner-banks-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.partner-banks-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.carousel-container {
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.carousel-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  background: white;
  padding: 2rem;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
  gap: 2rem;
}

.bank-slide {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 160px;
  justify-content: center;
}

.bank-slide:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-color);
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
}

.carousel-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-50%) scale(1.1);
}

.carousel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.carousel-btn.prev {
  left: -24px;
}

.carousel-btn.next {
  right: -24px;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

.bank-logo {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f1f5f9;
  padding: 8px;
  transition: all 0.3s ease;
}

.bank-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.bank-info {
  text-align: center;
}

.bank-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.bank-desc {
  font-size: 0.75rem;
  color: var(--text-light);
  line-height: 1.2;
}

.bank-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #e2e8f0, transparent);
}

/* Element Plus Carousel 样式覆盖 */
:deep(.el-carousel__indicator) {
  background-color: rgba(30, 58, 138, 0.3);
}

:deep(.el-carousel__indicator.is-active) {
  background-color: var(--primary-color);
}

:deep(.el-carousel__arrow) {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid #e2e8f0;
}

:deep(.el-carousel__arrow:hover) {
  background-color: var(--primary-color);
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .banks-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .bank-slide {
    flex: 0 0 250px;
  }

  .carousel-btn.prev {
    left: -20px;
  }

  .carousel-btn.next {
    right: -20px;
  }
}

@media (max-width: 768px) {
  .partner-banks-section {
    padding: 80px 0;
  }

  .carousel-wrapper {
    padding: 1.5rem;
  }

  .bank-slide {
    flex: 0 0 200px;
    padding: 1rem;
    height: 120px;
  }

  .bank-logo {
    width: 50px;
    height: 50px;
    padding: 6px;
  }

  .bank-name {
    font-size: 0.8rem;
  }

  .bank-desc {
    font-size: 0.7rem;
  }

  .bank-stats {
    gap: 1rem;
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .carousel-btn {
    width: 40px;
    height: 40px;
  }

  .carousel-btn.prev {
    left: -16px;
  }

  .carousel-btn.next {
    right: -16px;
  }
}

@media (max-width: 480px) {
  .bank-carousel {
    height: 180px;
  }

  .banks-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
    padding: 1rem;
  }

  .bank-item {
    height: 100px;
    flex-direction: row;
    text-align: left;
    gap: 0.8rem;
  }

  .bank-info {
    text-align: left;
    flex: 1;
  }

  .bank-logo {
    width: 45px;
    height: 45px;
    flex-shrink: 0;
  }

  .bank-name {
    font-size: 0.85rem;
    margin-bottom: 0.2rem;
  }

  .bank-desc {
    font-size: 0.7rem;
  }
}
</style>
