// 合作银行数据配置
export const partnerBanks = [
  {
    name: '百信银行',
    description: '数字化银行先锋',
    logo: '/banks/baixin.png'
  },
  {
    name: '苏商银行',
    description: '江苏地区优质银行',
    logo: '/banks/sushang.png'
  },
  {
    name: '蓝海银行',
    description: '新兴互联网银行',
    logo: '/banks/lanhai.png'
  },
  {
    name: '新网银行',
    description: '数字科技银行',
    logo: '/banks/xinwang.png'
  },
  {
    name: '广州农商银行',
    description: '华南地区领先的农商银行',
    logo: '/banks/nongshang.png'
  },
  {
    name: '平安银行',
    description: '中国领先的股份制商业银行',
    logo: '/banks/pingan.png'
  },
  {
    name: '兴业银行',
    description: '绿色金融领军银行',
    logo: '/banks/xingye.png'
  },
  {
    name: '中国建设银行',
    description: '国有大型商业银行',
    logo: '/banks/jianshe.png'
  },
  {
    name: '招商银行',
    description: '零售银行领先品牌',
    logo: '/banks/zhaoshang.png'
  },
  {
    name: '中国邮政储蓄银行',
    description: '服务三农的国有大行',
    logo: '/banks/youchu.png'
  },
  {
    name: '中国工商银行',
    description: '全球最大银行之一',
    logo: '/banks/gongshang.png'
  }
]

// 获取银行数据的函数
export const getPartnerBanks = () => {
  return partnerBanks
}

// 获取银行总数
export const getBankCount = () => {
  return partnerBanks.length
}
