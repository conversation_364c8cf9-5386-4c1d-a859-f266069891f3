<template>
  <div class="solutions-page">
    <!-- 页面头部 -->
    <section class="page-hero">
      <div class="container-wide">
        <div class="hero-content">
          <h1 class="page-title">解决方案</h1>
          <p class="page-subtitle">
            基于银行级技术架构，为不同规模的电商企业提供定制化的分账系统解决方案
          </p>
        </div>
      </div>
    </section>

    <!-- 解决方案列表 -->
    <section class="solutions-section">
      <div class="container-wide">
        <div class="solutions-grid">
          <div class="solution-card" v-for="solution in solutions" :key="solution.id">
            <div class="card-header">
              <div class="solution-icon">
                <el-icon :size="48">
                  <component :is="solution.icon" />
                </el-icon>
              </div>
              <div class="solution-meta">
                <h3 class="solution-title">{{ solution.title }}</h3>
                <p class="solution-desc">{{ solution.description }}</p>
              </div>
            </div>
            
            <div class="solution-features">
              <h4>核心功能</h4>
              <ul class="feature-list">
                <li v-for="feature in solution.features" :key="feature">
                  <el-icon class="feature-icon"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>
            
            <div class="solution-benefits">
              <h4>业务价值</h4>
              <div class="benefits-grid">
                <div class="benefit-item" v-for="benefit in solution.benefits" :key="benefit.label">
                  <div class="benefit-value">{{ benefit.value }}</div>
                  <div class="benefit-label">{{ benefit.label }}</div>
                </div>
              </div>
            </div>
            
            <div class="solution-action">
              <el-button type="primary" @click="learnMore(solution.id)">
                了解详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 新增产品解决方案 -->
    <section class="new-products-section">
      <div class="container">
        <h2 class="section-title">产品解决方案</h2>
        <div class="products-grid">
          <!-- 收单服务 -->
          <div class="product-card">
            <div class="product-header">
              <div class="product-icon">
                <el-icon :size="48">
                  <CreditCard />
                </el-icon>
              </div>
              <h3 class="product-title">收单服务</h3>
              <p class="product-desc">多渠道支付聚合，一站式收单解决方案</p>
            </div>

            <div class="product-features">
              <h4>核心功能</h4>
              <ul class="feature-list">
                <li v-for="feature in paymentFeatures" :key="feature">
                  <el-icon class="feature-icon"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>

            <div class="product-benefits">
              <h4>业务价值</h4>
              <div class="benefits-grid">
                <div class="benefit-item" v-for="benefit in paymentBenefits" :key="benefit.label">
                  <div class="benefit-value">{{ benefit.value }}</div>
                  <div class="benefit-label">{{ benefit.label }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 供应链金融 -->
          <div class="product-card">
            <div class="product-header">
              <div class="product-icon">
                <el-icon :size="48">
                  <TrendCharts />
                </el-icon>
              </div>
              <h3 class="product-title">供应链金融</h3>
              <p class="product-desc">基于真实交易数据的供应链金融服务</p>
            </div>

            <div class="product-features">
              <h4>核心功能</h4>
              <ul class="feature-list">
                <li v-for="feature in financeFeatures" :key="feature">
                  <el-icon class="feature-icon"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>

            <div class="product-benefits">
              <h4>业务价值</h4>
              <div class="benefits-grid">
                <div class="benefit-item" v-for="benefit in financeBenefits" :key="benefit.label">
                  <div class="benefit-value">{{ benefit.value }}</div>
                  <div class="benefit-label">{{ benefit.label }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 跨境结汇 -->
          <div class="product-card">
            <div class="product-header">
              <div class="product-icon">
                <el-icon :size="48">
                  <Position />
                </el-icon>
              </div>
              <h3 class="product-title">跨境结汇</h3>
              <p class="product-desc">专业的跨境电商结汇服务解决方案</p>
            </div>

            <div class="product-features">
              <h4>核心功能</h4>
              <ul class="feature-list">
                <li v-for="feature in settlementFeatures" :key="feature">
                  <el-icon class="feature-icon"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>

            <div class="product-benefits">
              <h4>业务价值</h4>
              <div class="benefits-grid">
                <div class="benefit-item" v-for="benefit in settlementBenefits" :key="benefit.label">
                  <div class="benefit-value">{{ benefit.value }}</div>
                  <div class="benefit-label">{{ benefit.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import {
  Connection,
  Timer,
  TrendCharts,
  Lock,
  Check,
  CreditCard,
  Position
} from '@element-plus/icons-vue'

const solutions = [
  {
    id: 'aggregation',
    icon: Connection,
    title: '多平台资金聚合',
    description: '统一管理抖音、天猫、京东等20+电商平台的资金收入',
    features: [
      '支持20+主流电商平台',
      'API自动对接，0代码接入',
      '实时数据同步',
      '统一资金视图',
      '多维度数据分析'
    ],
    benefits: [
      { value: '20+', label: '支持平台' },
      { value: '5分钟', label: '接入时间' },
      { value: '99.9%', label: '数据准确率' },
      { value: '90%', label: '管理效率提升' }
    ]
  },
  {
    id: 'realtime',
    icon: Timer,
    title: 'T+0实时到账',
    description: '突破传统银行限制，实现资金实时到账，提升资金使用效率',
    features: [
      '7×24小时服务',
      '无限制提现额度',
      '秒级到账速度',
      '节假日正常服务',
      '多种提现方式'
    ],
    benefits: [
      { value: 'T+0', label: '到账时效' },
      { value: '3秒', label: '最快到账' },
      { value: '无限制', label: '提现额度' },
      { value: '180%', label: '资金效率提升' }
    ]
  },
  {
    id: 'distribution',
    icon: TrendCharts,
    title: '智能分账系统',
    description: '基于预设规则自动分账，支持供应商、员工、合作伙伴批量结算',
    features: [
      '灵活分账规则配置',
      '批量处理能力',
      '实时状态监控',
      '自动对账功能',
      '异常处理机制'
    ],
    benefits: [
      { value: '1000+', label: '并发处理' },
      { value: '毫秒级', label: '处理速度' },
      { value: '100%', label: '准确率' },
      { value: '300%', label: '效率提升' }
    ]
  },
  {
    id: 'security',
    icon: Lock,
    title: '银行级安全',
    description: '央行监管的银行级安全保障，多重安全措施，资金安全无忧',
    features: [
      '央行监管合规',
      '资金完全隔离',
      '多重身份验证',
      '实时风控监控',
      '数据加密传输'
    ],
    benefits: [
      { value: '银行级', label: '安全等级' },
      { value: '100%', label: '资金隔离' },
      { value: '0', label: '安全事故' },
      { value: '24/7', label: '监控保护' }
    ]
  }
]

// 收单服务功能特性
const paymentFeatures = [
  '支持20+主流支付方式',
  '智能路由分发',
  '实时交易监控',
  '灵活费率配置',
  '风险防控体系'
]

const paymentBenefits = [
  { value: '99.9%', label: '支付成功率' },
  { value: '0.3%', label: '综合费率' },
  { value: '7×24', label: '服务时间' },
  { value: '秒级', label: '到账速度' }
]

// 供应链金融功能特性
const financeFeatures = [
  '应收账款融资',
  '库存质押融资',
  '订单融资服务',
  '智能风控评估',
  '灵活还款方式'
]

const financeBenefits = [
  { value: '72小时', label: '最快放款' },
  { value: '200万', label: '最高额度' },
  { value: '6%起', label: '年化利率' },
  { value: '90%', label: '最高融资比例' }
]

// 跨境结汇功能特性
const settlementFeatures = [
  '多币种收款支持',
  '合规结汇服务',
  '优惠汇率政策',
  '快速资金到账',
  '专业外汇服务'
]

const settlementBenefits = [
  { value: '200+', label: '覆盖国家' },
  { value: 'T+1', label: '结汇时效' },
  { value: '优惠', label: '汇率政策' },
  { value: '100%', label: '合规保障' }
]

const learnMore = (solutionId) => {
  console.log('了解更多:', solutionId)
}
</script>

<style scoped>
.solutions-page {
  flex: 1;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.solutions-section {
  padding: 100px 0;
  background: white;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.solution-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.solution-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.solution-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  flex-shrink: 0;
}

.solution-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.solution-desc {
  color: var(--text-light);
  line-height: 1.6;
}

.solution-features,
.solution-benefits {
  margin-bottom: 2rem;
}

.solution-features h4,
.solution-benefits h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  color: var(--text-color);
}

.feature-icon {
  color: var(--secondary-color);
  font-size: 1rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.benefit-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.benefit-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.benefit-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.3rem;
}

.solution-action {
  text-align: center;
}

.new-products-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
}

.product-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.product-header {
  text-align: center;
  margin-bottom: 2rem;
}

.product-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  margin: 0 auto 1.5rem;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.product-desc {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.product-features {
  margin-bottom: 2rem;
}

.product-features h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.5rem 0;
  color: var(--text-color);
}

.feature-icon {
  color: var(--secondary-color);
  font-size: 1rem;
}

.product-benefits h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.benefit-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.benefit-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.benefit-label {
  font-size: 0.9rem;
  color: var(--text-light);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    text-align: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .product-card {
    padding: 2rem;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }
}
</style>
