<template>
  <div class="home">
    <!-- 英雄区 -->
    <HeroSection />
    
    <!-- 核心数据展示 -->
    <DataCards />
    
    <!-- 痛点分析 -->
    <PainPointSection />
    
    <!-- 解决方案优势 -->
    <SolutionAdvantages />

    <!-- 收单服务 -->
    <PaymentAcquisitionSection />

    <!-- 供应链金融 -->
    <SupplyChainFinanceSection />

    <!-- 跨境结汇 -->
    <CrossBorderSettlementSection />

    <!-- 合作银行 -->
    <PartnerBanks />

    <!-- CTA区域 -->
    <CallToAction />
  </div>
</template>

<script setup>
import HeroSection from '../components/HeroSection.vue'
import DataCards from '../components/DataCards.vue'
import PainPointSection from '../components/PainPointSection.vue'
import SolutionAdvantages from '../components/SolutionAdvantages.vue'
import PaymentAcquisitionSection from '../components/PaymentAcquisitionSection.vue'
import SupplyChainFinanceSection from '../components/SupplyChainFinanceSection.vue'
import CrossBorderSettlementSection from '../components/CrossBorderSettlementSection.vue'
import PartnerBanks from '../components/PartnerBanks.vue'
import CallToAction from '../components/CallToAction.vue'
</script>

<style scoped>
.home {
  flex: 1;
}
</style>
