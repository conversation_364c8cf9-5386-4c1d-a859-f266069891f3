<template>
  <div class="about-page">
    <!-- 页面头部 -->
    <section class="page-hero">
      <div class="container-wide">
        <div class="hero-content">
          <h1 class="page-title">关于我们</h1>
          <p class="page-subtitle">
            专注于为电商企业提供专业的分账系统服务，让资金管理变得简单高效
          </p>
        </div>
      </div>
    </section>

    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container-wide">
        <div class="intro-content">
          <div class="intro-text">
            <h2 class="section-title">企业使命</h2>
            <p class="mission-text">
              让每一家电商企业都能享受到银行级的资金管理服务，通过技术创新降低企业运营成本，
              提升资金使用效率，助力电商行业的数字化转型。
            </p>
            
            <div class="company-values">
              <div class="value-item" v-for="value in companyValues" :key="value.title">
                <div class="value-icon">
                  <el-icon :size="32">
                    <component :is="value.icon" />
                  </el-icon>
                </div>
                <h4 class="value-title">{{ value.title }}</h4>
                <p class="value-desc">{{ value.description }}</p>
              </div>
            </div>
          </div>
          
          <div class="intro-visual">
            <div class="company-stats">
              <div class="stat-item" v-for="stat in companyStats" :key="stat.label">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- 合作银行 -->
    <PartnerBanks :show-arrows="false" :enable-drag="true" />

    <!-- 资质认证 -->
    <section class="certifications-section">
      <div class="container">
        <h2 class="section-title">资质认证</h2>
        <div class="certifications-grid">
          <div class="cert-item" v-for="cert in certifications" :key="cert.name">
            <div class="cert-icon">
              <el-icon :size="48">
                <component :is="cert.icon" />
              </el-icon>
            </div>
            <h4 class="cert-name">{{ cert.name }}</h4>
            <p class="cert-desc">{{ cert.description }}</p>
            <div class="cert-number">{{ cert.number }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-section">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-content">
          <div class="contact-info">
            <div class="contact-item" v-for="contact in contactInfo" :key="contact.type">
              <div class="contact-icon">
                <el-icon :size="24">
                  <component :is="contact.icon" />
                </el-icon>
              </div>
              <div class="contact-details">
                <div class="contact-label">{{ contact.label }}</div>
                <div class="contact-value">{{ contact.value }}</div>
              </div>
            </div>
          </div>
          
          <div class="contact-form">
            <h3>留言咨询</h3>
            <el-form :model="contactForm" label-width="80px">
              <el-form-item label="姓名">
                <el-input v-model="contactForm.name" placeholder="请输入您的姓名" />
              </el-form-item>
              <el-form-item label="公司">
                <el-input v-model="contactForm.company" placeholder="请输入公司名称" />
              </el-form-item>
              <el-form-item label="电话">
                <el-input v-model="contactForm.phone" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="contactForm.email" placeholder="请输入邮箱地址" />
              </el-form-item>
              <el-form-item label="留言">
                <el-input 
                  v-model="contactForm.message" 
                  type="textarea" 
                  :rows="4"
                  placeholder="请描述您的需求或问题"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitForm">提交留言</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  Lock,
  TrendCharts,
  UserFilled,
  Service,
  Check,
  Phone,
  Message,
  Location,
  Timer,
  Star
} from '@element-plus/icons-vue'
import PartnerBanks from '../components/PartnerBanks.vue'

const contactForm = ref({
  name: '',
  company: '',
  phone: '',
  email: '',
  message: ''
})

const companyValues = [
  {
    icon: Lock,
    title: '安全可靠',
    description: '银行级安全保障，资金安全是我们的第一要务'
  },
  {
    icon: TrendCharts,
    title: '创新驱动',
    description: '持续技术创新，为客户提供最优的解决方案'
  },
  {
    icon: UserFilled,
    title: '客户至上',
    description: '以客户需求为导向，提供专业贴心的服务'
  },
  {
    icon: Service,
    title: '专业服务',
    description: '7×24小时专业服务，确保业务连续性'
  }
]

const companyStats = [
  { value: '2019', label: '成立年份' },
  { value: '500+', label: '团队规模' },
  { value: '3,200+', label: '服务企业' },
  { value: '99.9%', label: '系统稳定性' }
]





const certifications = [
  {
    icon: Lock,
    name: 'ISO27001认证',
    description: '信息安全管理体系国际标准认证',
    number: 'ISO27001:2013'
  },
  {
    icon: Lock,
    name: '等保三级',
    description: '国家信息安全等级保护三级认证',
    number: '等保备案号：31011433001-30001'
  },
  {
    icon: Star,
    name: '支付业务许可证',
    description: '中国人民银行颁发的支付业务许可证',
    number: '许可证编号：Z2021110000001'
  },
  {
    icon: Service,
    name: 'PCI DSS认证',
    description: '支付卡行业数据安全标准认证',
    number: 'PCI DSS Level 1'
  }
]

import { getAboutContactInfo } from '../config/contact.js'

const contactInfo = getAboutContactInfo().map(contact => ({
  type: contact.type,
  icon: contact.type === 'phone' ? Phone :
        contact.type === 'email' ? Message :
        contact.type === 'address' ? Location : Timer,
  label: contact.label,
  value: contact.value
}))

const submitForm = () => {
  console.log('提交表单:', contactForm.value)
  // 这里可以添加表单提交逻辑
}
</script>

<style scoped>
.about-page {
  flex: 1;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.company-intro {
  padding: 100px 0;
  background: white;
}

.intro-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
}

.mission-text {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--text-light);
  margin-bottom: 3rem;
}

.company-values {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.value-item {
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 16px;
}

.value-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
  margin: 0 auto 1rem;
}

.value-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.value-desc {
  color: var(--text-light);
  line-height: 1.5;
}

.company-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 16px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
}

.stat-label {
  color: var(--text-light);
  margin-top: 0.5rem;
}





.certifications-section {
  padding: 100px 0;
  background: white;
}

.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.cert-item {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.cert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  margin: 0 auto 1.5rem;
}

.cert-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.cert-desc {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.cert-number {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.9rem;
}

.contact-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
}

.contact-label {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.3rem;
}

.contact-value {
  color: var(--text-light);
}

.contact-form {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .intro-content {
    grid-template-columns: 1fr;
  }

  .company-values {
    grid-template-columns: 1fr;
  }

  .company-stats {
    grid-template-columns: 1fr;
  }



  .contact-content {
    grid-template-columns: 1fr;
  }
}
</style>
